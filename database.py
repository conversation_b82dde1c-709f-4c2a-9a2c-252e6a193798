"""
数据库操作模块
"""
import logging
import uuid
import time
from typing import Dict, List, Optional
from config import get_db_connection

# 配置日志
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        pass
    
    def save_processing_result(self, status_code: int, body: Dict, processing_time: float, 
                             request_id: Optional[str] = None) -> str:
        """
        保存处理结果到数据库
        
        Args:
            status_code: HTTP状态码
            body: 响应体内容
            processing_time: 处理时间(秒)
            request_id: 请求ID，如果为None则自动生成
            
        Returns:
            str: 请求ID
        """
        if request_id is None:
            request_id = str(uuid.uuid4())
        
        connection = None
        try:
            connection = get_db_connection()
            cursor = connection.cursor()
            
            # 提取body中的信息
            success = body.get('success', False)
            message = body.get('message', '')
            results = body.get('results', [])
            
            # 统计文件处理结果
            total_files = len(results)
            success_files = sum(1 for r in results if r.get('status') == 'success')
            failed_files = total_files - success_files
            
            # 插入主记录
            insert_main_sql = """
                INSERT INTO processing_records 
                (request_id, status_code, success, message, total_files, success_files, failed_files, processing_time)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_main_sql, (
                request_id, status_code, success, message, 
                total_files, success_files, failed_files, processing_time
            ))
            
            # 插入文件结果记录
            if results:
                insert_file_sql = """
                    INSERT INTO file_results 
                    (request_id, file_path, status, error_message, output_path)
                    VALUES (%s, %s, %s, %s, %s)
                """
                file_data = []
                for result in results:
                    file_data.append((
                        request_id,
                        result.get('file_path', ''),
                        result.get('status', 'failed'),
                        result.get('error'),
                        result.get('output_path')
                    ))
                cursor.executemany(insert_file_sql, file_data)
            
            connection.commit()
            logger.info(f"成功保存处理结果到数据库，请求ID: {request_id}")
            return request_id
            
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"保存处理结果到数据库失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            return request_id if request_id else str(uuid.uuid4())
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接失败: {str(e)}")
    
    def log_database_operation(self, operation: str, success: bool, error_message: str = None):
        """
        记录数据库操作日志
        
        Args:
            operation: 操作类型
            success: 是否成功
            error_message: 错误信息
        """
        if success:
            logger.info(f"数据库操作成功: {operation}")
        else:
            logger.error(f"数据库操作失败: {operation}, 错误: {error_message}")


def create_tables():
    """
    创建数据库表（如果不存在）
    """
    connection = None
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 创建处理记录主表
        create_main_table_sql = """
            CREATE TABLE IF NOT EXISTS processing_records (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                request_id VARCHAR(64) NOT NULL COMMENT '请求唯一标识',
                status_code INT NOT NULL COMMENT 'HTTP状态码',
                success BOOLEAN NOT NULL COMMENT '整体处理是否成功',
                message TEXT COMMENT '处理消息',
                total_files INT NOT NULL DEFAULT 0 COMMENT '总文件数',
                success_files INT NOT NULL DEFAULT 0 COMMENT '成功处理文件数',
                failed_files INT NOT NULL DEFAULT 0 COMMENT '失败处理文件数',
                processing_time DECIMAL(10,2) COMMENT '总处理时间(秒)',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_request_id (request_id),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处理记录主表'
        """
        
        # 创建文件处理结果表
        create_file_table_sql = """
            CREATE TABLE IF NOT EXISTS file_results (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                request_id VARCHAR(64) NOT NULL COMMENT '关联的请求ID',
                file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
                status ENUM('success', 'failed') NOT NULL COMMENT '处理状态',
                error_message TEXT COMMENT '错误信息',
                output_path VARCHAR(1000) COMMENT '输出文件路径',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                INDEX idx_request_id (request_id),
                INDEX idx_file_path (file_path(255)),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件处理结果表'
        """
        
        cursor.execute(create_main_table_sql)
        cursor.execute(create_file_table_sql)
        connection.commit()
        
        logger.info("数据库表创建/检查完成")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {str(e)}")
        if connection:
            connection.rollback()
    finally:
        if connection:
            try:
                connection.close()
            except Exception as e:
                logger.error(f"关闭数据库连接失败: {str(e)}")


# 全局数据库管理器实例
db_manager = DatabaseManager()
