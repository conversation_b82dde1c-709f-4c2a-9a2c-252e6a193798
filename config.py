"""
配置文件
"""
import os
import pymysql

# 通义千问使用OpenAI兼容API的配置
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
APPLICATION_ID = "6789f3ee97dc459f884e2af4cc2b9daf"
MAX_FILE_SIZE_MB = 15
INPUT_PATH = "/mnt/fileinput/"
OUTPUT_PATH = "/mnt/fileoutput/"
# INPUT_PATH = "/code"
# OUTPUT_PATH = "/code"

def get_db_connection():
    """获取数据库连接"""
    url, port = os.environ.get("DB_ADDRESS").split(":")
    return pymysql.connect(
        host=url,
        # port=int(port),
        user=os.environ.get("DB_USERNAME"),
        password=os.environ.get("DB_PASSWORD"),
        database=os.environ.get("DB_NAME"),
        # charset="utf8mb4",
        connect_timeout=20
    )