# 测试指南

## 概述

`handler.py` 是新的函数计算入口点，支持正式环境和测试环境。在阿里云函数计算中，您可以通过发送特定的测试事件来测试各种功能。

## 测试方式

### 1. 数据库功能测试

在阿里云函数计算控制台中，使用以下测试事件：

```json
{
  "test_type": "database"
}
```

**测试内容：**
- 检查数据库环境变量配置
- 测试数据库连接
- 测试表创建/检查
- 测试数据保存功能

**预期返回：**
```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "message": "数据库功能测试通过",
    "test_type": "database_functionality",
    "test_request_id": "生成的请求ID"
  }
}
```

### 2. 处理功能测试

在阿里云函数计算控制台中，使用以下测试事件：

```json
{
  "test_type": "processing"
}
```

**测试内容：**
- 模拟文件处理流程
- 测试返回值结构
- 测试数据库存储

**预期返回：**
```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "message": "处理功能测试完成，成功1个，失败1个",
    "results": [
      {
        "file_path": "test/mock_file1.pdf",
        "status": "success",
        "error": null,
        "output_path": "output/mock_file1.jpg"
      },
      {
        "file_path": "test/mock_file2.pdf",
        "status": "failed",
        "error": "no sensitive info",
        "output_path": null
      }
    ],
    "test_type": "processing_functionality",
    "test_request_id": "生成的请求ID"
  }
}
```

### 3. 正式处理测试

使用真实的文件路径进行测试：

```json
{
  "file_path": "your/real/file.pdf"
}
```

或批量处理：

```json
{
  "file_path": [
    "your/real/file1.pdf",
    "your/real/file2.pdf"
  ]
}
```

## 环境变量配置

确保在阿里云函数计算中配置了以下环境变量：

```bash
# 数据库配置
DB_ADDRESS=your-db-host:3306
DB_USERNAME=your-username
DB_PASSWORD=your-password
DB_NAME=your-database-name

# 其他必要配置
OPENAI_API_KEY=your-api-key
```

## 测试步骤

### 步骤1：配置环境变量
在阿里云函数计算控制台中设置所有必要的环境变量。

### 步骤2：测试数据库功能
1. 在控制台中创建测试事件，内容为 `{"test_type": "database"}`
2. 执行测试
3. 检查返回结果和日志

### 步骤3：测试处理功能
1. 在控制台中创建测试事件，内容为 `{"test_type": "processing"}`
2. 执行测试
3. 检查返回结果和日志

### 步骤4：验证数据库存储
1. 连接到数据库
2. 查询 `processing_records` 和 `file_results` 表
3. 确认测试数据已正确保存

## 错误排查

### 常见错误及解决方案

#### 1. 数据库连接失败
**错误信息：** `数据库连接失败: ...`

**解决方案：**
- 检查 `DB_ADDRESS`, `DB_USERNAME`, `DB_PASSWORD`, `DB_NAME` 环境变量
- 确认数据库服务可访问
- 检查网络连接

#### 2. 缺少环境变量
**错误信息：** `缺少必要的环境变量: [...]`

**解决方案：**
- 在函数计算控制台中设置缺少的环境变量
- 确保变量名称正确

#### 3. 权限不足
**错误信息：** `Access denied for user ...`

**解决方案：**
- 确认数据库用户有创建表和插入数据的权限
- 检查数据库用户配置

## 本地测试

如果需要在本地测试，可以直接运行：

```bash
# 设置环境变量
export DB_ADDRESS="your-db-host:3306"
export DB_USERNAME="your-username"
export DB_PASSWORD="your-password"
export DB_NAME="your-database-name"
export OPENAI_API_KEY="your-api-key"

# 运行测试
python handler.py
```

## 日志查看

在阿里云函数计算控制台的日志中，您可以看到：

- 数据库连接状态
- 表创建/检查结果
- 数据保存操作结果
- 详细的错误信息（如果有）

**成功日志示例：**
```
[2024-01-01 12:00:00] [INFO] [handler.py] [test_database_functionality] - 数据库连接测试成功
[2024-01-01 12:00:00] [INFO] [handler.py] [test_database_functionality] - 数据库表创建/检查成功
[2024-01-01 12:00:00] [INFO] [database.py] [save_processing_result] - 成功保存处理结果到数据库，请求ID: abc123
```

## 生产环境使用

在生产环境中，正常发送包含 `file_path` 的事件即可：

```json
{
  "file_path": "production/file.pdf"
}
```

系统会自动：
1. 处理文件
2. 保存结果到数据库
3. 返回处理结果
4. 记录详细日志

第三方可以通过查询数据库获取处理结果，无需等待函数同步返回。
