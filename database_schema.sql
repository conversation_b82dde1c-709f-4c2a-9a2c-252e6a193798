-- 数据库表结构设计
-- 用于存储项目运行结果的结构化数据

-- 处理记录主表
CREATE TABLE IF NOT EXISTS processing_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(64) NOT NULL COMMENT '请求唯一标识',
    status_code INT NOT NULL COMMENT 'HTTP状态码',
    success BOOLEAN NOT NULL COMMENT '整体处理是否成功',
    message TEXT COMMENT '处理消息',
    total_files INT NOT NULL DEFAULT 0 COMMENT '总文件数',
    success_files INT NOT NULL DEFAULT 0 COMMENT '成功处理文件数',
    failed_files INT NOT NULL DEFAULT 0 COMMENT '失败处理文件数',
    processing_time DECIMAL(10,2) COMMENT '总处理时间(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_request_id (request_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status_code (status_code),
    INDEX idx_success (success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处理记录主表';

-- 文件处理结果表
CREATE TABLE IF NOT EXISTS file_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_id VARCHAR(64) NOT NULL COMMENT '关联的请求ID',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    status ENUM('success', 'failed') NOT NULL COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    output_path VARCHAR(1000) COMMENT '输出文件路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_request_id (request_id),
    INDEX idx_file_path (file_path(255)),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件处理结果表';

-- 查询示例
-- 1. 查询最近的处理记录
-- SELECT * FROM processing_records ORDER BY created_at DESC LIMIT 10;

-- 2. 查询某个请求的详细文件处理结果
-- SELECT pr.*, fr.file_path, fr.status, fr.error_message, fr.output_path 
-- FROM processing_records pr 
-- LEFT JOIN file_results fr ON pr.request_id = fr.request_id 
-- WHERE pr.request_id = 'your_request_id';

-- 3. 统计成功率
-- SELECT 
--     DATE(created_at) as date,
--     COUNT(*) as total_requests,
--     SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
--     ROUND(SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
-- FROM processing_records 
-- GROUP BY DATE(created_at) 
-- ORDER BY date DESC;

-- 4. 查询处理时间统计
-- SELECT 
--     AVG(processing_time) as avg_time,
--     MIN(processing_time) as min_time,
--     MAX(processing_time) as max_time,
--     COUNT(*) as total_count
-- FROM processing_records 
-- WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 5. 查询错误类型统计
-- SELECT 
--     error_message,
--     COUNT(*) as error_count
-- FROM file_results 
-- WHERE status = 'failed' 
--   AND error_message IS NOT NULL
--   AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
-- GROUP BY error_message 
-- ORDER BY error_count DESC;
