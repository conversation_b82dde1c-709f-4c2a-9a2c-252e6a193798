#!/usr/bin/env python3
"""
查询处理结果示例脚本
供第三方调用，用于查询数据库中的处理结果
"""
import os
import sys
import logging
from datetime import datetime, timedelta
from config import get_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class ResultQuery:
    """处理结果查询类"""
    
    def __init__(self):
        """初始化查询器"""
        pass
    
    def get_processing_result_by_request_id(self, request_id: str) -> dict:
        """
        根据请求ID查询处理结果
        
        Args:
            request_id: 请求ID
            
        Returns:
            dict: 包含主记录和文件结果的完整信息
        """
        connection = None
        try:
            connection = get_db_connection()
            cursor = connection.cursor()
            
            # 查询主记录
            cursor.execute("""
                SELECT request_id, status_code, success, message, total_files, 
                       success_files, failed_files, processing_time, created_at
                FROM processing_records 
                WHERE request_id = %s
            """, (request_id,))
            
            main_record = cursor.fetchone()
            if not main_record:
                return {"error": "未找到指定的请求记录"}
            
            # 查询文件结果
            cursor.execute("""
                SELECT file_path, status, error_message, output_path, created_at
                FROM file_results 
                WHERE request_id = %s
                ORDER BY created_at
            """, (request_id,))
            
            file_results = cursor.fetchall()
            
            # 组装结果
            result = {
                "request_id": main_record[0],
                "status_code": main_record[1],
                "success": bool(main_record[2]),
                "message": main_record[3],
                "total_files": main_record[4],
                "success_files": main_record[5],
                "failed_files": main_record[6],
                "processing_time": float(main_record[7]) if main_record[7] else None,
                "created_at": main_record[8].isoformat() if main_record[8] else None,
                "file_results": []
            }
            
            for file_result in file_results:
                result["file_results"].append({
                    "file_path": file_result[0],
                    "status": file_result[1],
                    "error_message": file_result[2],
                    "output_path": file_result[3],
                    "created_at": file_result[4].isoformat() if file_result[4] else None
                })
            
            return result
            
        except Exception as e:
            logger.error(f"查询处理结果失败: {str(e)}")
            return {"error": f"查询失败: {str(e)}"}
        finally:
            if connection:
                connection.close()
    
    def get_recent_results(self, limit: int = 10) -> list:
        """
        获取最近的处理结果
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            list: 最近的处理结果列表
        """
        connection = None
        try:
            connection = get_db_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT request_id, status_code, success, message, total_files, 
                       success_files, failed_files, processing_time, created_at
                FROM processing_records 
                ORDER BY created_at DESC 
                LIMIT %s
            """, (limit,))
            
            records = cursor.fetchall()
            results = []
            
            for record in records:
                results.append({
                    "request_id": record[0],
                    "status_code": record[1],
                    "success": bool(record[2]),
                    "message": record[3],
                    "total_files": record[4],
                    "success_files": record[5],
                    "failed_files": record[6],
                    "processing_time": float(record[7]) if record[7] else None,
                    "created_at": record[8].isoformat() if record[8] else None
                })
            
            return results
            
        except Exception as e:
            logger.error(f"查询最近结果失败: {str(e)}")
            return []
        finally:
            if connection:
                connection.close()
    
    def get_statistics(self, days: int = 1) -> dict:
        """
        获取统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            dict: 统计信息
        """
        connection = None
        try:
            connection = get_db_connection()
            cursor = connection.cursor()
            
            # 计算时间范围
            start_time = datetime.now() - timedelta(days=days)
            
            # 查询统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                    SUM(total_files) as total_files,
                    SUM(success_files) as total_success_files,
                    AVG(processing_time) as avg_processing_time,
                    MIN(processing_time) as min_processing_time,
                    MAX(processing_time) as max_processing_time
                FROM processing_records 
                WHERE created_at >= %s
            """, (start_time,))
            
            stats = cursor.fetchone()
            
            if stats and stats[0] > 0:
                return {
                    "period_days": days,
                    "total_requests": stats[0],
                    "success_requests": stats[1],
                    "success_rate": round((stats[1] / stats[0]) * 100, 2) if stats[0] > 0 else 0,
                    "total_files": stats[2] or 0,
                    "total_success_files": stats[3] or 0,
                    "file_success_rate": round((stats[3] / stats[2]) * 100, 2) if stats[2] > 0 else 0,
                    "avg_processing_time": round(float(stats[4]), 2) if stats[4] else 0,
                    "min_processing_time": round(float(stats[5]), 2) if stats[5] else 0,
                    "max_processing_time": round(float(stats[6]), 2) if stats[6] else 0
                }
            else:
                return {
                    "period_days": days,
                    "total_requests": 0,
                    "message": "指定时间范围内没有处理记录"
                }
            
        except Exception as e:
            logger.error(f"查询统计信息失败: {str(e)}")
            return {"error": f"查询统计信息失败: {str(e)}"}
        finally:
            if connection:
                connection.close()


def main():
    """主函数 - 演示查询功能"""
    query = ResultQuery()
    
    print("=== 处理结果查询示例 ===\n")
    
    # 1. 查询最近的处理结果
    print("1. 最近的处理结果:")
    recent_results = query.get_recent_results(5)
    if recent_results:
        for result in recent_results:
            print(f"  请求ID: {result['request_id']}")
            print(f"  状态: {'成功' if result['success'] else '失败'}")
            print(f"  消息: {result['message']}")
            print(f"  处理时间: {result['processing_time']}秒")
            print(f"  创建时间: {result['created_at']}")
            print("-" * 40)
    else:
        print("  没有找到处理记录")
    
    # 2. 查询统计信息
    print("\n2. 最近24小时统计:")
    stats = query.get_statistics(1)
    if "error" not in stats:
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功请求数: {stats['success_requests']}")
        print(f"  成功率: {stats['success_rate']}%")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  成功文件数: {stats['total_success_files']}")
        print(f"  文件成功率: {stats['file_success_rate']}%")
        print(f"  平均处理时间: {stats['avg_processing_time']}秒")
    else:
        print(f"  {stats['error']}")
    
    # 3. 如果有参数，查询指定请求ID的详细结果
    if len(sys.argv) > 1:
        request_id = sys.argv[1]
        print(f"\n3. 请求 {request_id} 的详细结果:")
        detail = query.get_processing_result_by_request_id(request_id)
        if "error" not in detail:
            print(f"  状态码: {detail['status_code']}")
            print(f"  成功: {detail['success']}")
            print(f"  消息: {detail['message']}")
            print(f"  总文件数: {detail['total_files']}")
            print(f"  成功文件数: {detail['success_files']}")
            print(f"  处理时间: {detail['processing_time']}秒")
            print(f"  文件结果:")
            for file_result in detail['file_results']:
                print(f"    - {file_result['file_path']}: {file_result['status']}")
                if file_result['error_message']:
                    print(f"      错误: {file_result['error_message']}")
                if file_result['output_path']:
                    print(f"      输出: {file_result['output_path']}")
        else:
            print(f"  {detail['error']}")


if __name__ == "__main__":
    main()
