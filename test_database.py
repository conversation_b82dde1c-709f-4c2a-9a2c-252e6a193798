#!/usr/bin/env python3
"""
数据库功能测试脚本
"""
import os
import sys
import logging
import time
from database import db_manager, create_tables

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def test_database_connection():
    """测试数据库连接"""
    try:
        from database import get_db_connection
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        connection.close()
        logger.info("数据库连接测试成功")
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        return False


def test_create_tables():
    """测试创建表"""
    try:
        create_tables()
        logger.info("创建表测试成功")
        return True
    except Exception as e:
        logger.error(f"创建表测试失败: {str(e)}")
        return False


def test_save_processing_result():
    """测试保存处理结果"""
    try:
        # 模拟成功的处理结果
        test_body_success = {
            'success': True,
            'message': '处理完成，成功2个，失败1个',
            'results': [
                {
                    'file_path': 'test/file1.pdf',
                    'status': 'success',
                    'error': None,
                    'output_path': 'output/file1.jpg'
                },
                {
                    'file_path': 'test/file2.pdf',
                    'status': 'success',
                    'error': None,
                    'output_path': 'output/file2.jpg'
                },
                {
                    'file_path': 'test/file3.pdf',
                    'status': 'failed',
                    'error': 'no sensitive info',
                    'output_path': None
                }
            ]
        }
        
        request_id = db_manager.save_processing_result(
            status_code=200,
            body=test_body_success,
            processing_time=15.67
        )
        logger.info(f"保存成功处理结果测试成功，请求ID: {request_id}")
        
        # 模拟失败的处理结果
        test_body_failed = {
            'success': False,
            'message': '处理失败: 文件不存在'
        }
        
        request_id = db_manager.save_processing_result(
            status_code=500,
            body=test_body_failed,
            processing_time=2.34
        )
        logger.info(f"保存失败处理结果测试成功，请求ID: {request_id}")
        
        return True
    except Exception as e:
        logger.error(f"保存处理结果测试失败: {str(e)}")
        return False


def test_query_results():
    """测试查询结果"""
    try:
        from config import get_db_connection
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 查询最近的处理记录
        cursor.execute("""
            SELECT request_id, status_code, success, message, total_files, 
                   success_files, failed_files, processing_time, created_at
            FROM processing_records 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        logger.info(f"查询到 {len(records)} 条最近的处理记录:")
        for record in records:
            logger.info(f"  请求ID: {record[0]}, 状态码: {record[1]}, 成功: {record[2]}, "
                       f"消息: {record[3]}, 处理时间: {record[7]}秒")
        
        # 查询文件处理结果
        if records:
            request_id = records[0][0]
            cursor.execute("""
                SELECT file_path, status, error_message, output_path
                FROM file_results 
                WHERE request_id = %s
            """, (request_id,))
            
            file_results = cursor.fetchall()
            logger.info(f"请求 {request_id} 的文件处理结果:")
            for file_result in file_results:
                logger.info(f"  文件: {file_result[0]}, 状态: {file_result[1]}, "
                           f"错误: {file_result[2]}, 输出: {file_result[3]}")
        
        connection.close()
        logger.info("查询结果测试成功")
        return True
    except Exception as e:
        logger.error(f"查询结果测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    logger.info("开始数据库功能测试")
    
    # 检查环境变量
    required_env_vars = ['DB_ADDRESS', 'DB_USERNAME', 'DB_PASSWORD', 'DB_NAME']
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        logger.error("请设置以下环境变量:")
        for var in required_env_vars:
            logger.error(f"  export {var}=your_value")
        return False
    
    tests = [
        ("数据库连接", test_database_connection),
        ("创建表", test_create_tables),
        ("保存处理结果", test_save_processing_result),
        ("查询结果", test_query_results)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"执行测试: {test_name}")
        if test_func():
            passed += 1
            logger.info(f"✓ {test_name} 测试通过")
        else:
            logger.error(f"✗ {test_name} 测试失败")
        logger.info("-" * 50)
    
    logger.info(f"测试完成: {passed}/{total} 个测试通过")
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
