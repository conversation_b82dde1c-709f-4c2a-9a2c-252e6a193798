# 数据库存储功能说明

## 概述

为了解决阿里云函数计算运行缓慢导致第三方无法及时获取返回结果的问题，本项目新增了数据库存储功能。现在所有的处理结果都会自动保存到数据库中，第三方可以通过查询数据库来获取处理结果。

## 功能特性

- ✅ **结构化存储**: 返回值的每个字段都对应数据库中的独立列，禁止存储JSON字符串
- ✅ **完整日志记录**: 无论落库成功还是失败，都会记录详细日志
- ✅ **保留原返回值**: 在不影响原有功能的基础上新增数据库存储
- ✅ **异常处理**: 数据库操作失败不会影响主流程执行
- ✅ **自动表创建**: 首次运行时自动创建所需的数据库表

## 数据库表结构

### 1. 处理记录主表 (processing_records)

存储每次处理请求的总体信息：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| request_id | VARCHAR(64) | 请求唯一标识 |
| status_code | INT | HTTP状态码 (200/400/500) |
| success | BOOLEAN | 整体处理是否成功 |
| message | TEXT | 处理消息 |
| total_files | INT | 总文件数 |
| success_files | INT | 成功处理文件数 |
| failed_files | INT | 失败处理文件数 |
| processing_time | DECIMAL(10,2) | 总处理时间(秒) |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 2. 文件处理结果表 (file_results)

存储每个文件的详细处理结果：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| request_id | VARCHAR(64) | 关联的请求ID |
| file_path | VARCHAR(1000) | 文件路径 |
| status | ENUM('success', 'failed') | 处理状态 |
| error_message | TEXT | 错误信息 |
| output_path | VARCHAR(1000) | 输出文件路径 |
| created_at | TIMESTAMP | 创建时间 |

## 环境变量配置

需要设置以下数据库相关的环境变量：

```bash
# 数据库地址 (格式: host:port)
export DB_ADDRESS="your-db-host:3306"

# 数据库用户名
export DB_USERNAME="your-username"

# 数据库密码
export DB_PASSWORD="your-password"

# 数据库名称
export DB_NAME="your-database-name"
```

## 使用方法

### 1. 数据库初始化

首次使用前，请执行以下SQL脚本创建数据库表：

```bash
mysql -h your-host -u your-username -p your-database < database_schema.sql
```

或者直接运行项目，系统会自动创建所需的表。

### 2. 测试数据库功能

运行测试脚本验证数据库功能：

```bash
python test_database.py
```

### 3. 查询处理结果

#### 查询最近的处理记录
```sql
SELECT * FROM processing_records ORDER BY created_at DESC LIMIT 10;
```

#### 查询某个请求的详细结果
```sql
SELECT pr.*, fr.file_path, fr.status, fr.error_message, fr.output_path 
FROM processing_records pr 
LEFT JOIN file_results fr ON pr.request_id = fr.request_id 
WHERE pr.request_id = 'your_request_id';
```

#### 统计成功率
```sql
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_requests,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
    ROUND(SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM processing_records 
GROUP BY DATE(created_at) 
ORDER BY date DESC;
```

## 代码变更说明

### 新增文件

1. **database.py**: 数据库操作模块
   - `DatabaseManager`: 数据库管理类
   - `create_tables()`: 创建数据库表函数
   - `db_manager`: 全局数据库管理器实例

2. **database_schema.sql**: 数据库表结构定义
3. **test_database.py**: 数据库功能测试脚本
4. **DATABASE_README.md**: 数据库功能说明文档

### 修改文件

1. **config.py**: 添加了 `get_db_connection()` 函数
2. **index.py**: 在 `handler()` 函数中集成数据库存储功能
3. **requirements.txt**: 添加了 `pymysql` 依赖

## 日志记录

系统会记录以下类型的数据库操作日志：

- ✅ 数据库连接状态
- ✅ 表创建操作
- ✅ 数据插入操作（成功/失败）
- ✅ 错误详情记录

示例日志：
```
[2024-01-01 12:00:00] [INFO] [database.py] [save_processing_result] - 成功保存处理结果到数据库，请求ID: abc123
[2024-01-01 12:00:00] [INFO] [database.py] [log_database_operation] - 数据库操作成功: 保存成功处理结果
```

## 注意事项

1. **数据库连接**: 确保数据库服务可访问且环境变量配置正确
2. **表权限**: 确保数据库用户有创建表和插入数据的权限
3. **异常处理**: 数据库操作失败不会影响主流程，但会记录错误日志
4. **性能考虑**: 数据库操作在主流程完成后执行，不影响处理性能
5. **数据清理**: 建议定期清理历史数据以保持数据库性能

## 故障排除

### 常见问题

1. **连接失败**: 检查环境变量和网络连接
2. **权限不足**: 确保数据库用户有足够权限
3. **表不存在**: 运行 `create_tables()` 或执行SQL脚本

### 调试方法

1. 运行测试脚本: `python test_database.py`
2. 检查日志输出
3. 手动连接数据库验证配置
