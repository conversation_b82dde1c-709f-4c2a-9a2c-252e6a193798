"""
阿里云函数计算入口文件
支持正式环境和测试环境
"""
import json
import logging
import sys
import os
import time
from offer_blur import OfferBlur
import main
import gc
from database import db_manager, create_tables, get_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def test_database_functionality():
    """测试数据库功能"""
    logger.info("开始测试数据库功能...")
    
    # 检查环境变量
    required_env_vars = ['DB_ADDRESS', 'DB_USERNAME', 'DB_PASSWORD', 'DB_NAME']
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        return {
            'statusCode': 500,
            'body': {
                'success': False,
                'message': f'数据库配置错误，缺少环境变量: {missing_vars}',
                'test_type': 'database_config_check'
            }
        }
    
    try:
        # 测试数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        connection.close()
        logger.info("数据库连接测试成功")
        
        # 测试创建表
        create_tables()
        logger.info("数据库表创建/检查成功")
        
        # 测试保存处理结果
        test_body = {
            'success': True,
            'message': '数据库功能测试',
            'results': [
                {
                    'file_path': 'test/database_test.pdf',
                    'status': 'success',
                    'error': None,
                    'output_path': 'output/database_test.jpg'
                }
            ]
        }
        
        request_id = db_manager.save_processing_result(
            status_code=200,
            body=test_body,
            processing_time=1.23
        )
        logger.info(f"数据库保存测试成功，请求ID: {request_id}")
        
        return {
            'statusCode': 200,
            'body': {
                'success': True,
                'message': '数据库功能测试通过',
                'test_type': 'database_functionality',
                'test_request_id': request_id
            }
        }
        
    except Exception as e:
        logger.error(f"数据库功能测试失败: {str(e)}")
        return {
            'statusCode': 500,
            'body': {
                'success': False,
                'message': f'数据库功能测试失败: {str(e)}',
                'test_type': 'database_functionality'
            }
        }


def test_processing_functionality():
    """测试处理功能（模拟）"""
    logger.info("开始测试处理功能...")
    
    try:
        # 模拟处理结果
        mock_results = [
            {
                'file_path': 'test/mock_file1.pdf',
                'status': 'success',
                'error': None,
                'output_path': 'output/mock_file1.jpg'
            },
            {
                'file_path': 'test/mock_file2.pdf',
                'status': 'failed',
                'error': 'no sensitive info',
                'output_path': None
            }
        ]
        
        response = {
            'statusCode': 200,
            'body': {
                'success': True,
                'message': '处理功能测试完成，成功1个，失败1个',
                'results': mock_results,
                'test_type': 'processing_functionality'
            }
        }
        
        # 尝试保存到数据库
        try:
            request_id = db_manager.save_processing_result(
                response['statusCode'], 
                response['body'], 
                2.45
            )
            response['body']['test_request_id'] = request_id
            db_manager.log_database_operation("保存测试处理结果", True)
        except Exception as db_e:
            logger.warning(f"保存测试结果到数据库失败: {str(db_e)}")
            db_manager.log_database_operation("保存测试处理结果", False, str(db_e))
        
        return response
        
    except Exception as e:
        logger.error(f"处理功能测试失败: {str(e)}")
        return {
            'statusCode': 500,
            'body': {
                'success': False,
                'message': f'处理功能测试失败: {str(e)}',
                'test_type': 'processing_functionality'
            }
        }


def handler(event, context):
    """
    阿里云函数计算入口函数
    支持正式环境和测试环境
    
    :param event: 事件对象，包含请求参数
    :param context: 上下文对象
    :return: 处理结果，始终返回统一的结构
    """
    blur = OfferBlur()
    request_id = None
    start_time = time.time()
    
    # 确保数据库表存在
    try:
        create_tables()
    except Exception as e:
        logger.warning(f"创建数据库表时出现警告: {str(e)}")
    
    try:
        # 解析请求参数
        if isinstance(event, str):
            event_str = event
        else:
            # 先将二进制解码为字符串，默认使用 UTF-8 编码
            event_str = event.decode('utf-8')
        
        logger.info(f"接收到的事件: {event_str}")
        
        # 然后解析JSON字符串
        json_data = OfferBlur.extract_json_from_text(event_str, "index")
        logger.info(f"提取到的JSON字符串: {json_data}")
        
        if json_data:
            event = blur.parse_nested_json(json_data)
            logger.info(f"json解析后的事件: {event}")
        
        # 检查是否是测试请求
        test_type = event.get('test_type')
        if test_type:
            logger.info(f"检测到测试请求，类型: {test_type}")
            
            if test_type == 'database':
                return test_database_functionality()
            elif test_type == 'processing':
                return test_processing_functionality()
            else:
                return {
                    'statusCode': 400,
                    'body': {
                        'success': False,
                        'message': f'不支持的测试类型: {test_type}，支持的类型: database, processing',
                        'test_type': test_type
                    }
                }
        
        # 正式处理逻辑
        # 获取文件路径
        file_path = event.get('file_path')
        if not file_path:
            response = {
                'statusCode': 400,
                'body': {
                    'success': False,
                    'message': '缺少必要的file_path参数'
                }
            }
            # 保存错误结果到数据库
            processing_time = time.time() - start_time
            try:
                request_id = db_manager.save_processing_result(
                    response['statusCode'],
                    response['body'],
                    processing_time
                )
                db_manager.log_database_operation("保存参数错误结果", True)
            except Exception as db_e:
                db_manager.log_database_operation("保存参数错误结果", False, str(db_e))
            
            return response

        # 处理PDF文件
        logger.info(f"PDF文件的路径是: {file_path}")

        results = []
        processing_start_time = time.time()
        # 检查file_path是否为列表，以进行批量处理
        if isinstance(file_path, list):
            logger.info(f"接收到文件路径列表，共{len(file_path)}个文件")
            # 批量处理文件列表
            results = main.batch_process_files(file_path)
        else:
            # 单个文件处理，将结果放入列表保持一致格式
            result = blur.process_pdf(file_path)
            results = [result]
        elapsed_time = time.time() - processing_start_time
        logger.info(f"批次总处理时间: {elapsed_time:.2f}秒")
        # 提取成功处理的输出路径
        output_paths = [r.get('output_path') for r in results if r.get('status') == 'success']

        response = {
            'statusCode': 200,
            'body': {
                'success': len(output_paths) > 0,  # 至少有一个成功处理的文件
                'message': f'处理完成，成功{len(output_paths)}个，失败{len(results) - len(output_paths)}个',
                'results': results
            }
        }
        
        # 保存成功结果到数据库
        total_processing_time = time.time() - start_time
        try:
            request_id = db_manager.save_processing_result(
                response['statusCode'],
                response['body'],
                total_processing_time
            )
            db_manager.log_database_operation("保存成功处理结果", True)
        except Exception as db_e:
            db_manager.log_database_operation("保存成功处理结果", False, str(db_e))
        
        return response

    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        response = {
            'statusCode': 500,
            'body': {
                'success': False,
                'message': f'处理失败: {str(e)}'
            }
        }
        
        # 保存异常结果到数据库
        total_processing_time = time.time() - start_time
        try:
            request_id = db_manager.save_processing_result(
                response['statusCode'],
                response['body'],
                total_processing_time
            )
            db_manager.log_database_operation("保存异常处理结果", True)
        except Exception as db_e:
            db_manager.log_database_operation("保存异常处理结果", False, str(db_e))
        
        return response
    finally:
        # 确保资源被释放
        if blur:
            del blur


# 为了兼容性，保留原来的入口点
def main(event, context):
    """兼容性入口点"""
    return handler(event, context)


if __name__ == "__main__":
    """本地测试入口"""
    # 本地测试时可以直接运行
    test_event = {
        "test_type": "database"
    }
    
    class MockContext:
        pass
    
    result = handler(json.dumps(test_event), MockContext())
    print(json.dumps(result, indent=2, ensure_ascii=False))
