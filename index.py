import json
import logging
import sys
from offer_blur import OfferBlur
import main
import gc
import time
from database import db_manager, create_tables

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def handler(event, context):
    """
    阿里云函数计算入口函数
    :param event: 事件对象，包含请求参数
    :param context: 上下文对象
    :return: 处理结果，始终返回统一的结构
    """
    blur = OfferBlur()
    request_id = None
    start_time = time.time()

    # 确保数据库表存在
    try:
        create_tables()
    except Exception as e:
        logger.warning(f"创建数据库表时出现警告: {str(e)}")

    try:
        # 解析请求参数
        if isinstance(event, str):
            event_str = event
        else:
            # 先将二进制解码为字符串，默认使用 UTF-8 编码
            event_str = event.decode('utf-8')
        logger.info(f"接收到的事件: {event_str}")
        # 然后解析JSON字符串
        json_data = OfferBlur.extract_json_from_text(event_str,"index")
        logger.info(f"提取到的JSON字符串: {json_data}")
        if json_data:
            event = blur.parse_nested_json(json_data)
            logger.info(f"json解析后的事件: {event}")
        # 获取文件路径
        file_path = event.get('file_path')
        if not file_path:
            response = {
                'statusCode': 400,
                'body': {
                    'success': False,
                    'message': '缺少必要的file_path参数'
                }
            }
            # 保存错误结果到数据库
            processing_time = time.time() - start_time
            try:
                request_id = db_manager.save_processing_result(
                    response['statusCode'],
                    response['body'],
                    processing_time
                )
                db_manager.log_database_operation("保存参数错误结果", True)
            except Exception as db_e:
                db_manager.log_database_operation("保存参数错误结果", False, str(db_e))

            return response

        # 处理PDF文件
        logger.info(f"PDF文件的路径是: {file_path}")

        results = []
        processing_start_time = time.time()
        # 检查file_path是否为列表，以进行批量处理
        if isinstance(file_path, list):
            logger.info(f"接收到文件路径列表，共{len(file_path)}个文件")
            # 批量处理文件列表
            results = main.batch_process_files(file_path)
        else:
            # 单个文件处理，将结果放入列表保持一致格式
            result = blur.process_pdf(file_path)
            results = [result]
        elapsed_time = time.time() - processing_start_time
        logger.info(f"批次总处理时间: {elapsed_time:.2f}秒")
        # 提取成功处理的输出路径
        output_paths = [r.get('output_path') for r in results if r.get('status') == 'success']

        response = {
            'statusCode': 200,
            'body': {
                'success': len(output_paths) > 0,  # 至少有一个成功处理的文件
                'message': f'处理完成，成功{len(output_paths)}个，失败{len(results) - len(output_paths)}个',
                'results': results
            }
        }

        # 保存成功结果到数据库
        total_processing_time = time.time() - start_time
        try:
            request_id = db_manager.save_processing_result(
                response['statusCode'],
                response['body'],
                total_processing_time
            )
            db_manager.log_database_operation("保存成功处理结果", True)
        except Exception as db_e:
            db_manager.log_database_operation("保存成功处理结果", False, str(db_e))

        return response

    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        response = {
            'statusCode': 500,
            'body': {
                'success': False,
                'message': f'处理失败: {str(e)}'
            }
        }

        # 保存异常结果到数据库
        total_processing_time = time.time() - start_time
        try:
            request_id = db_manager.save_processing_result(
                response['statusCode'],
                response['body'],
                total_processing_time
            )
            db_manager.log_database_operation("保存异常处理结果", True)
        except Exception as db_e:
            db_manager.log_database_operation("保存异常处理结果", False, str(db_e))

        return response
    finally:
        # 确保资源被释放
        if blur:
            del blur
